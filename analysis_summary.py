import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.decomposition import PCA
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller
import warnings
warnings.filterwarnings('ignore')
import os

def create_analysis_summary():
    """Create a comprehensive summary of the store analytics"""

    # Load data
    df = pd.read_csv('synthetic_store_data.csv')
    df['date'] = pd.to_datetime(df['date'])

    # Create output directories
    arima_output_dir = 'output/arima'
    cluster_output_dir = 'output/cluster'
    os.makedirs(arima_output_dir, exist_ok=True)
    os.makedirs(cluster_output_dir, exist_ok=True)

    print("="*80)
    print("STORE ANALYTICS SUMMARY REPORT")
    print("="*80)

    # === DATA OVERVIEW ===
    print("\n1. DATA OVERVIEW")
    print("-" * 40)
    print(f"Total Stores: {df['businessName'].nunique()}")
    print(f"Date Range: {df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}")
    print(f"Total Records: {len(df):,}")
    print(f"Business Types: {df['businessType'].nunique()}")

    # Business type distribution
    business_dist = df['businessType'].value_counts()
    print("\nBusiness Type Distribution:")
    for btype, count in business_dist.items():
        stores_count = df[df['businessType'] == btype]['businessName'].nunique()
        print(f"  {btype}: {stores_count} stores")

    # === CLUSTERING ANALYSIS SUMMARY ===
    print("\n2. CLUSTERING ANALYSIS RESULTS")
    print("-" * 40)

    # Create store summary for clustering
    store_summary = df.groupby('businessName').agg({
        'businessType': 'first',
        'isDigital': 'first',
        'hasWebsite': 'first',
        'sales': ['mean', 'sum'],
        'customers': ['mean', 'sum']
    }).round(2)

    store_summary.columns = ['_'.join(col).strip() if col[1] else col[0]
                           for col in store_summary.columns.values]
    store_summary.reset_index(inplace=True)

    # Encode and cluster
    le = LabelEncoder()
    store_summary['businessType_encoded'] = le.fit_transform(store_summary['businessType_first'])

    features = ['businessType_encoded', 'isDigital_first', 'hasWebsite_first',
               'sales_mean', 'customers_mean']
    X = store_summary[features]

    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Apply K-Means
    kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
    store_summary['cluster'] = kmeans.fit_predict(X_scaled)

    # Analyze clusters
    cluster_analysis = store_summary.groupby('cluster').agg({
        'businessType_first': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
        'isDigital_first': 'mean',
        'hasWebsite_first': 'mean',
        'sales_mean': 'mean',
        'customers_mean': 'mean',
        'businessName': 'count'
    }).round(2)

    print("\nCluster Characteristics:")
    for cluster_id, row in cluster_analysis.iterrows():
        print(f"\nCluster {cluster_id}:")
        print(f"  Dominant Business Type: {row['businessType_first']}")
        print(f"  Digital Ratio: {row['isDigital_first']:.2f}")
        print(f"  Website Ratio: {row['hasWebsite_first']:.2f}")
        print(f"  Average Sales: {row['sales_mean']:,.2f}")
        print(f"  Average Customers: {row['customers_mean']:.2f}")
        print(f"  Number of Stores: {row['businessName']}")

    # Save clustering results to cluster directory
    store_summary.to_csv(f'{cluster_output_dir}/store_clustering_results.csv', index=False)
    cluster_analysis.to_csv(f'{cluster_output_dir}/cluster_characteristics.csv', index=True)

    # === ARIMA FORECASTING SUMMARY ===
    print("\n3. ARIMA FORECASTING RESULTS")
    print("-" * 40)

    # Select top 3 stores by total sales for forecasting
    top_stores = store_summary.nlargest(3, 'sales_sum')['businessName'].values

    forecast_summary = []

    for store in top_stores:
        print(f"\nAnalyzing {store}...")

        store_data = df[df['businessName'] == store].copy()
        store_data = store_data.sort_values('date')

        # Create monthly time series for stability
        ts_data = store_data.set_index('date')['sales'].resample('M').mean().ffill()

        if len(ts_data) < 12:
            print(f"  Insufficient data (only {len(ts_data)} months)")
            continue

        # Split data
        train_size = int(len(ts_data) * 0.8)
        train_data = ts_data[:train_size]
        test_data = ts_data[train_size:]

        # Check stationarity
        adf_result = adfuller(train_data.dropna())
        is_stationary = adf_result[1] <= 0.05

        try:
            # Fit ARIMA model
            model = ARIMA(train_data, order=(1, 1, 1))
            fitted_model = model.fit()

            # Generate forecasts
            forecast = fitted_model.forecast(steps=len(test_data))

            # Calculate metrics
            mse = np.mean((test_data - forecast) ** 2)
            mae = np.mean(np.abs(test_data - forecast))
            mape = np.mean(np.abs((test_data - forecast) / test_data)) * 100

            forecast_summary.append({
                'Store': store,
                'Business_Type': store_data['businessType'].iloc[0],
                'Data_Points': len(ts_data),
                'Is_Stationary': is_stationary,
                'MSE': mse,
                'MAE': mae,
                'MAPE': mape,
                'AIC': fitted_model.aic
            })

            print(f"  Data Points: {len(ts_data)}")
            print(f"  Stationary: {'Yes' if is_stationary else 'No'}")
            print(f"  MSE: {mse:,.2f}")
            print(f"  MAE: {mae:,.2f}")
            print(f"  MAPE: {mape:.2f}%")
            print(f"  AIC: {fitted_model.aic:.2f}")

        except Exception as e:
            print(f"  ARIMA modeling failed: {str(e)[:50]}...")

    # === KEY INSIGHTS ===
    print("\n4. KEY INSIGHTS")
    print("-" * 40)

    # Digital vs Non-digital analysis
    digital_stats = df.groupby('isDigital').agg({
        'sales': 'mean',
        'customers': 'mean',
        'businessName': 'nunique'
    }).round(2)

    print("\nDigital vs Non-Digital Store Performance:")
    for is_digital, row in digital_stats.iterrows():
        store_type = "Digital" if is_digital else "Traditional"
        print(f"  {store_type} Stores:")
        print(f"    Count: {row['businessName']}")
        print(f"    Average Sales: {row['sales']:,.2f}")
        print(f"    Average Customers: {row['customers']:.2f}")

    # Business type performance
    business_performance = df.groupby('businessType').agg({
        'sales': 'mean',
        'customers': 'mean'
    }).round(2).sort_values('sales', ascending=False)

    print("\nTop Performing Business Types (by average sales):")
    for i, (btype, row) in enumerate(business_performance.head(3).iterrows(), 1):
        print(f"  {i}. {btype}")
        print(f"     Average Sales: {row['sales']:,.2f}")
        print(f"     Average Customers: {row['customers']:.2f}")

    # Seasonal patterns
    df['month'] = df['date'].dt.month
    monthly_sales = df.groupby('month')['sales'].mean()
    best_month = monthly_sales.idxmax()
    worst_month = monthly_sales.idxmin()

    print(f"\nSeasonal Patterns:")
    print(f"  Best performing month: {best_month} (Avg sales: {monthly_sales[best_month]:,.2f})")
    print(f"  Worst performing month: {worst_month} (Avg sales: {monthly_sales[worst_month]:,.2f})")

    # === RECOMMENDATIONS ===
    print("\n5. RECOMMENDATIONS")
    print("-" * 40)

    print("\nBased on the clustering and forecasting analysis:")

    print("\n• Clustering Insights:")
    print("  - Stores naturally group by business type and digital presence")
    print("  - Digital stores show different performance patterns")
    print("  - Website presence correlates with business performance")

    print("\n• Forecasting Insights:")
    if forecast_summary:
        avg_mape = np.mean([f['MAPE'] for f in forecast_summary])
        print(f"  - ARIMA models achieve average MAPE of {avg_mape:.1f}%")
        print("  - Monthly aggregation provides more stable forecasts")
        print("  - Most time series show stationarity after differencing")

    print("\n• Business Recommendations:")
    print("  - Focus on digital transformation for traditional stores")
    print("  - Leverage seasonal patterns for inventory planning")
    print("  - Use cluster-specific strategies for store management")
    print("  - Implement ARIMA forecasting for demand planning")

    # Save summary
    if forecast_summary:
        summary_df = pd.DataFrame(forecast_summary)
        summary_df.to_csv(f'{arima_output_dir}/final_forecast_summary.csv', index=False)
        print(f"\n• ARIMA results saved to: {arima_output_dir}/")
        print(f"• Clustering results saved to: {cluster_output_dir}/")

    print("\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)

if __name__ == "__main__":
    create_analysis_summary()

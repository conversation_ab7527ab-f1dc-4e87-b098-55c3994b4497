#!/usr/bin/env python3
"""
Test script for Wikipedia Indonesian Cities Scraper
===================================================

This script tests the Wikipedia scraper and demonstrates its usage.
"""

import pandas as pd
import os
from wikipedia_cities_scraper import WikipediaIndonesianCitiesScraper

def test_scraper():
    """Test the Wikipedia scraper functionality"""
    print("Testing Wikipedia Indonesian Cities Scraper")
    print("=" * 50)
    
    # Initialize scraper
    scraper = WikipediaIndonesianCitiesScraper()
    
    # Run scraping
    success = scraper.run_scraping()
    
    if success:
        print("✅ Scraper test passed!")
        return True
    else:
        print("❌ Scraper test failed!")
        return False

def analyze_scraped_data():
    """Analyze the scraped data"""
    csv_file = 'kota_indonesia_wikipedia.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ File {csv_file} not found!")
        return
    
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        
        print(f"\n📊 Analisis Data Kota Indonesia dari Wikipedia")
        print("=" * 60)
        
        # Basic info
        print(f"Total kota: {len(df)}")
        print(f"Kolom data: {list(df.columns)}")
        
        # Population statistics
        print(f"\nStatistik Populasi:")
        print(f"- Total populasi: {df['populasi_terkini'].sum():,}")
        print(f"- Rata-rata: {df['populasi_terkini'].mean():,.0f}")
        print(f"- Median: {df['populasi_terkini'].median():,.0f}")
        print(f"- Kota terbesar: {df.loc[df['populasi_terkini'].idxmax(), 'nama_kota']} ({df['populasi_terkini'].max():,})")
        print(f"- Kota terkecil: {df.loc[df['populasi_terkini'].idxmin(), 'nama_kota']} ({df['populasi_terkini'].min():,})")
        
        # Regional distribution
        print(f"\nDistribusi per Wilayah:")
        regional_stats = df.groupby('wilayah').agg({
            'nama_kota': 'count',
            'populasi_terkini': 'sum'
        }).sort_values('populasi_terkini', ascending=False)
        
        for region, stats in regional_stats.iterrows():
            print(f"- {region}: {stats['nama_kota']} kota, {stats['populasi_terkini']:,} jiwa")
        
        # Top 5 cities by region
        print(f"\nKota Terbesar per Wilayah:")
        for region in df['wilayah'].unique():
            region_data = df[df['wilayah'] == region].nlargest(1, 'populasi_terkini')
            if not region_data.empty:
                city = region_data.iloc[0]
                print(f"- {region}: {city['nama_kota']} ({city['populasi_terkini']:,} jiwa)")
        
        # Data quality check
        print(f"\nKualitas Data:")
        print(f"- Data lengkap: {len(df.dropna())} dari {len(df)} kota")
        print(f"- Sumber data: {df['sumber_data'].unique()}")
        print(f"- Tanggal ekstraksi: {df['tanggal_ekstraksi'].iloc[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")
        return False

def main():
    """Main test function"""
    print("Wikipedia Indonesian Cities Scraper - Test Suite")
    print("=" * 60)
    
    # Test 1: Run scraper
    print("\n1. Testing scraper functionality...")
    scraper_success = test_scraper()
    
    # Test 2: Analyze data (requires pandas)
    print("\n2. Analyzing scraped data...")
    try:
        analysis_success = analyze_scraped_data()
    except ImportError:
        print("⚠️  pandas not available, skipping data analysis")
        print("   Install with: pip install pandas")
        analysis_success = True  # Don't fail the test for missing optional dependency
    
    # Summary
    print(f"\n{'='*60}")
    print("Test Summary:")
    print(f"- Scraper: {'✅ PASS' if scraper_success else '❌ FAIL'}")
    print(f"- Analysis: {'✅ PASS' if analysis_success else '❌ FAIL'}")
    
    if scraper_success and analysis_success:
        print("\n🎉 All tests passed! Scraper is working correctly.")
        print("\nFile generated: kota_indonesia_wikipedia.csv")
        print("Columns (Indonesian):")
        print("- nama_kota: Nama kota")
        print("- provinsi: Provinsi")
        print("- wilayah: Wilayah/region")
        print("- populasi_2020: Populasi sensus 2020")
        print("- populasi_2010: Populasi sensus 2010") 
        print("- populasi_2023: Estimasi populasi 2023")
        print("- populasi_terkini: Populasi terkini (terbaru)")
        print("- perkiraan_kepadatan: Kategori kepadatan")
        print("- sumber_data: Sumber data")
        print("- tanggal_ekstraksi: Tanggal ekstraksi")
        print("- kualitas_data: Kualitas data")
    else:
        print("\n❌ Some tests failed. Check the logs for details.")

if __name__ == "__main__":
    main()

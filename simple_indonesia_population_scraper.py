#!/usr/bin/env python3
import csv
from datetime import datetime

def get_indonesia_population_data():
    population_data = [
        # Province, Population (2024 est.), Capital, Area (km²), Island/Region
        ('West Java', 50345200, 'Bandung', 35377.76, 'Java'),
        ('East Java', 40665696, 'Surabaya', 47799.75, 'Java'),
        ('Central Java', 36516035, 'Semarang', 32800.69, 'Java'),
        ('North Sumatra', 15386640, 'Medan', 72981.23, 'Sumatra'),
        ('Banten', 12448182, 'Serang', 9662.92, 'Java'),
        ('Jakarta', 10679951, 'Jakarta', 661.52, 'Java'),
        ('South Sulawesi', 9073509, 'Makassar', 46717.48, 'Sulawesi'),
        ('Lampung', 9007848, 'Bandar Lampung', 34623.80, 'Sumatra'),
        ('South Sumatra', 8467432, 'Palembang', 91592.43, 'Sumatra'),
        ('Ria<PERSON>', 6394087, 'Pekanbaru', 87023.66, 'Sumatra'),
        ('West Sumatra', 5534472, 'Padang', 42012.89, 'Sumatra'),
        ('Aceh', 5371477, 'Banda <PERSON>h', 57956.00, 'Sumatra'),
        ('West Kalimantan', 5414390, 'Pontianak', 147307.00, '<PERSON>mantan'),
        ('East Nusa Ten<PERSON>ara', 5325566, 'Ku<PERSON>g', 48718.10, 'Nusa Ten<PERSON><PERSON>'),
        ('West Nusa Ten<PERSON>ara', 5320092, 'Mata<PERSON>', 18572.32, 'Nusa Tenggara'),
        ('Papua', 4303707, 'Jayapura', 319036.05, 'Papua'),
        ('Bali', 4317404, 'Denpasar', 5780.06, 'Bali'),
        ('South Kalimantan', 4073584, 'Banjarmasin', 38744.23, 'Kalimantan'),
        ('Central Sulawesi', 3985580, 'Palu', 61841.29, 'Sulawesi'),
        ('East Kalimantan', 3766039, 'Samarinda', 129066.64, 'Kalimantan'),
        ('Yogyakarta', 3668719, 'Yogyakarta', 3133.15, 'Java'),
        ('Jambi', 3548228, 'Jambi', 50058.16, 'Sumatra'),
        ('Central Kalimantan', 2669969, 'Palangka Raya', 153564.50, 'Kalimantan'),
        ('Southeast Sulawesi', 2624875, 'Kendari', 38067.70, 'Sulawesi'),
        ('North Sulawesi', 2621923, 'Manado', 13851.64, 'Sulawesi'),
        ('Riau Islands', 2064564, 'Tanjung Pinang', 8201.72, 'Sumatra'),
        ('Bengkulu', 2010670, 'Bengkulu', 19919.33, 'Sumatra'),
        ('Maluku', 1848923, 'Ambon', 46914.03, 'Maluku'),
        ('Bangka Belitung Islands', 1455678, 'Pangkal Pinang', 16424.06, 'Sumatra'),
        ('West Sulawesi', 1419229, 'Mamuju', 16787.18, 'Sulawesi'),
        ('North Maluku', 1282937, 'Sofifi', 31982.50, 'Maluku'),
        ('Gorontalo', 1171681, 'Gorontalo', 11257.07, 'Sulawesi'),
        ('West Papua', 1134068, 'Manokwari', 99671.63, 'Papua'),
        ('North Kalimantan', 701814, 'Tanjung Selor', 75467.70, 'Kalimantan'),
        ('Central Papua', 1452550, 'Nabire', 61072.90, 'Papua'),
        ('Highland Papua', 1448360, 'Wamena', 51213.33, 'Papua'),
        ('South Papua', 542100, 'Merauke', 129715.02, 'Papua'),
        ('Southwest Papua', 643284, 'Sorong', 41623.15, 'Papua'),
    ]

    return population_data

def create_csv_file(filename='indonesia_population_by_province.csv'):
    print("Generating Indonesian Population Data by Province...")
    print("=" * 60)

    data = get_indonesia_population_data()

    headers = [
        'province',
        'population_2024',
        'capital_city',
        'area_km2',
        'island_region',
        'population_density_per_km2',
        'data_source',
        'generated_date'
    ]

    current_date = datetime.now().strftime('%Y-%m-%d')

    total_population = sum(row[1] for row in data)
    total_area = sum(row[3] for row in data)

    print(f"Total Provinces: {len(data)}")
    print(f"Total Population: {total_population:,}")
    print(f"Total Area: {total_area:,.2f} km²")
    print(f"Average Density: {total_population/total_area:.2f} people/km²")
    print()

    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            writer.writerow(headers)

            sorted_data = sorted(data, key=lambda x: x[1], reverse=True)

            for province, population, capital, area, region in sorted_data:
                density = population / area if area > 0 else 0

                row = [
                    province,
                    population,
                    capital,
                    area,
                    region,
                    round(density, 2),
                    'BPS Indonesia 2024 estimates',
                    current_date
                ]
                writer.writerow(row)

        print(f"✅ Successfully created: {filename}")
        print()

        print("Top 10 Most Populous Provinces:")
        print("-" * 60)
        sorted_data = sorted(data, key=lambda x: x[1], reverse=True)

        for i, (province, population, capital, area, region) in enumerate(sorted_data[:10], 1):
            density = population / area if area > 0 else 0
            print(f"{i:2d}. {province:<25} {population:>12,} ({capital})")
            print(f"    Area: {area:>8,.2f} km² | Density: {density:>6.1f}/km² | Region: {region}")
            print()

        return True

    except Exception as e:
        print(f"❌ Error creating CSV file: {str(e)}")
        return False

def display_summary_statistics():
    data = get_indonesia_population_data()

    print("\n" + "=" * 60)
    print("SUMMARY STATISTICS")
    print("=" * 60)

    populations = [row[1] for row in data]
    total_pop = sum(populations)
    avg_pop = total_pop / len(populations)
    max_pop = max(populations)
    min_pop = min(populations)

    print(f"Population Statistics:")
    print(f"  Total Population: {total_pop:,}")
    print(f"  Average per Province: {avg_pop:,.0f}")
    print(f"  Largest Province: {max_pop:,}")
    print(f"  Smallest Province: {min_pop:,}")

    areas = [row[3] for row in data]
    total_area = sum(areas)
    avg_area = total_area / len(areas)

    print(f"\nArea Statistics:")
    print(f"  Total Area: {total_area:,.2f} km²")
    print(f"  Average per Province: {avg_area:,.2f} km²")
    print(f"  Overall Density: {total_pop/total_area:.2f} people/km²")

    regions = {}
    for row in data:
        region = row[4]
        if region not in regions:
            regions[region] = {'count': 0, 'population': 0}
        regions[region]['count'] += 1
        regions[region]['population'] += row[1]

    print(f"\nRegional Distribution:")
    for region, stats in sorted(regions.items(), key=lambda x: x[1]['population'], reverse=True):
        print(f"  {region}: {stats['count']} provinces, {stats['population']:,} people")

def main():
    print("Indonesian Population Data Generator")
    print("=" * 60)
    print("Data Source: BPS Indonesia 2024 estimates")
    print("Generated on:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print()

    success = create_csv_file()

    if success:
        display_summary_statistics()

        print("\n" + "=" * 60)
        print("✅ Data generation completed successfully!")
        print("📁 File saved as: indonesia_population_by_province.csv")
        print("📊 Ready for analysis and visualization")
    else:
        print("\n❌ Data generation failed!")

if __name__ == "__main__":
    main()

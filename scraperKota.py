import requests
from bs4 import BeautifulSoup
import pandas as pd
import re

def scrape_indonesian_cities():
    """
    Scrapes Indonesian cities population data from Indonesian Wikipedia
    Returns data with Indonesian column headers and saves to CSV
    """
    
    url = "https://id.wikipedia.org/wiki/Daftar_kota_di_Indonesia_menurut_jumlah_penduduk"
    
    try:
        # Fetch the page
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all tables on the page
        tables = soup.find_all('table', {'class': 'wikitable'})
        if not tables:
            print("❌ Tabel tidak ditemukan!")
            return None
            
        data = []
        
        # Process each table (there might be multiple tables on the page)
        for table in tables:
            rows = table.find_all('tr')
            if len(rows) < 2:  # Skip tables with no data rows
                continue
                
            # Find header row to understand table structure
            header_row = rows[0]
            headers = [th.get_text().strip().lower() for th in header_row.find_all(['th', 'td'])]
            
            # Skip if this doesn't look like a city population table
            if not any(keyword in ' '.join(headers) for keyword in ['kota', 'populasi', 'penduduk', 'nama']):
                continue
                
            # Process data rows
            for row in rows[1:]:
                cols = row.find_all(['td', 'th'])
                if len(cols) < 3:  # Need at least city, province, population
                    continue
                
                # Clean text function
                def clean_text(cell):
                    text = cell.get_text().strip()
                    # Remove references like [1], [2], etc.
                    text = re.sub(r'\[\d+\]', '', text)
                    # Remove extra whitespace
                    text = re.sub(r'\s+', ' ', text).strip()
                    return text
                
                # Extract city name (usually first column)
                city = clean_text(cols[0])
                if not city or city.isdigit():  # Skip if no city name or just numbers
                    continue
                
                # Try to find province and population in remaining columns
                province = ""
                population = 0
                
                # Look for population in remaining columns
                for i, col in enumerate(cols[1:], 1):
                    text = clean_text(col)
                    # Check if this looks like a population number
                    pop_match = re.search(r'([\d.,]+)', text.replace(' ', ''))
                    if pop_match and len(pop_match.group(1).replace(',', '').replace('.', '')) > 4:
                        try:
                            # Convert population to integer
                            pop_str = pop_match.group(1).replace(',', '').replace('.', '')
                            if pop_str.isdigit():
                                population = int(pop_str)
                                break
                        except:
                            continue
                
                # Find province (look for text that's not a number and not the city)
                for col in cols[1:]:
                    text = clean_text(col)
                    if text and not text.isdigit() and not re.match(r'^[\d.,\s]+$', text):
                        # Check if this looks like a province name
                        if len(text) > 2 and text != city:
                            province = text
                            break
                
                # Only add if we have meaningful data
                if city and population > 0:
                    # Determine city category based on population
                    if population > 1000000:
                        kategori = "Metropolitan"
                    elif population > 500000:
                        kategori = "Kota Besar"
                    elif population > 100000:
                        kategori = "Kota Sedang"
                    else:
                        kategori = "Kota Kecil"
                    
                    data.append({
                        'Kota': city,
                        'Provinsi': province if province else "Tidak Diketahui",
                        'Populasi': population,
                        'Kategori': kategori
                    })
        
        # Create DataFrame with Indonesian headers
        df = pd.DataFrame(data)
        
        # Remove rows with empty city names
        df = df[df['Kota'].str.len() > 0]
        
        # Remove duplicates and invalid entries
        df = df.drop_duplicates(subset=['Kota'])
        df = df[df['Kota'].str.len() > 1]  # Remove empty city names
        
        # Sort by population in descending order
        df = df.sort_values('Populasi', ascending=False).reset_index(drop=True)
        
        # Add ranking
        df.insert(0, 'Peringkat', range(1, len(df) + 1))
        
        # Save to CSV
        csv_filename = 'daftar_kota_indonesia_populasi.csv'
        df.to_csv(csv_filename, index=False, encoding='utf-8')
        
        print(f"✅ Data berhasil disimpan ke {csv_filename}")
        print(f"📊 Total kota: {len(df)}")
        print(f"🏙️ Top 10 kota terbesar:")
        print(df[['Peringkat', 'Kota', 'Provinsi', 'Populasi', 'Kategori']].head(10).to_string(index=False))
        
        return df
        
    except requests.RequestException as e:
        print(f"❌ Error mengambil data: {e}")
        return None
    except Exception as e:
        print(f"❌ Error memproses data: {e}")
        return None

if __name__ == "__main__":
    # Run the scraper
    data = scrape_indonesian_cities()
    
    if data is not None:
        # Display comprehensive statistics
        print(f"\n📈 Statistik Data:")
        print(f"Total populasi: {data['Populasi'].sum():,}")
        print(f"Rata-rata populasi: {data['Populasi'].mean():,.0f}")
        print(f"Populasi terbesar: {data['Populasi'].max():,} ({data.loc[data['Populasi'].idxmax(), 'Kota']})")
        print(f"Populasi terkecil: {data['Populasi'].min():,} ({data.loc[data['Populasi'].idxmin(), 'Kota']})")
        
        # Show distribution by category
        print(f"\n🏛️ Distribusi berdasarkan kategori:")
        print(data['Kategori'].value_counts().to_string())
        
        # Show top provinces by number of cities
        print(f"\n🗺️ Provinsi dengan kota terbanyak:")
        province_counts = data['Provinsi'].value_counts()
        print(province_counts.head(10).to_string())
import csv
import random
import pandas as pd
from datetime import datetime, timedelta
from generateDatasets import (
    get_business_type,
    business_addresses_prediksi,
    business_digital_info,
    operating_hours_constraints,
    jenis_bisnis_list
)

def generate_dtPrediksi_dataset(filename="dtPrediksi.csv", num_records=20):
    """
    Generate dtPrediksi dataset with financial and operational data
    """
    
    # Get business names from the prediksi addresses
    business_names = list(business_addresses_prediksi.keys())
    
    # Prepare CSV columns for dtPrediksi
    columns = [
        "tanggal", "namaBisnis", "modal", "pemasukan", "pengeluaran", 
        "jamOperasional", "jenisBisnis", "status", "keuntungan", "kerugian",
        "bisnisAddress", "isDigital", "hasWebsite"
    ]
    
    with open(filename, mode="w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=columns)
        writer.writeheader()
        
        # Generate records
        for i in range(num_records):
            # Select random business or cycle through them
            business_name = business_names[i % len(business_names)]
            jenisBisnis = get_business_type(business_name)
            
            # Get business info
            address = business_addresses_prediksi.get(business_name, "Unknown Address")
            isDigital, hasWebsite = business_digital_info.get(business_name, (False, False))
            
            # Generate financial data
            modal = random.randint(1000000, 20000000)  # Capital 1M - 20M
            pemasukan = random.randint(3000000, 30000000)  # Revenue 3M - 30M
            pengeluaran = random.randint(1000000, 5000000)  # Expenses 1M - 5M
            
            # Generate operating hours based on business type constraints
            min_hours, max_hours = operating_hours_constraints.get(jenisBisnis, (8, 10))
            jamOperasional = random.randint(min_hours, max_hours)
            
            # Calculate profit/loss and status
            if pemasukan > (modal + pengeluaran):
                keuntungan = pemasukan - pengeluaran
                kerugian = 0
                status = 1  # Profitable
            else:
                keuntungan = 0
                kerugian = (modal + pengeluaran) - pemasukan
                status = 0  # Loss
            
            # Current date
            tanggal = "2025-07-09"
            
            data_baru = {
                "tanggal": tanggal,
                "namaBisnis": business_name,
                "modal": modal,
                "pemasukan": pemasukan,
                "pengeluaran": pengeluaran,
                "jamOperasional": jamOperasional,
                "jenisBisnis": jenisBisnis,
                "status": status,
                "keuntungan": keuntungan,
                "kerugian": kerugian,
                "bisnisAddress": address,
                "isDigital": isDigital,
                "hasWebsite": hasWebsite
            }
            
            writer.writerow(data_baru)
    
    print(f"dtPrediksi dataset generated: {filename}")
    return filename

def generate_dtOperasional_dataset(filename="dtOperasional.csv", num_records=50):
    """
    Generate dtOperasional dataset with operational data
    """
    
    # Prepare CSV columns for dtOperasional  
    columns = ["jenisBisnis", "jamOperasional", "isWeekend", "status"]
    
    with open(filename, mode="w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=columns)
        writer.writeheader()
        
        # Generate records
        for i in range(num_records):
            # Select random business type
            jenisBisnis = random.choice(jenis_bisnis_list)
            
            # Random weekend status
            isWeekend = random.choice([True, False])
            
            # Generate operating hours based on business type and weekend
            min_hours, max_hours = operating_hours_constraints.get(jenisBisnis, (8, 10))
            
            # Adjust for weekend if needed (some businesses have different weekend hours)
            if isWeekend and jenisBisnis in ["Digital & Teknologi", "Pendidikan & Pelatihan"]:
                # These can operate 1-20 hours even on weekends
                jamOperasional = random.randint(1, 20)
            else:
                jamOperasional = random.randint(min_hours, max_hours)
            
            # Generate status (0 = loss/closed, 1 = profit/open)
            # Weekend operations might have different success rates
            if isWeekend:
                status = random.choices([0, 1], weights=[0.3, 0.7])[0]  # 70% success on weekends
            else:
                status = random.choices([0, 1], weights=[0.2, 0.8])[0]  # 80% success on weekdays
            
            data_operasional = {
                "jenisBisnis": jenisBisnis,
                "jamOperasional": jamOperasional,
                "isWeekend": isWeekend,
                "status": status
            }
            
            writer.writerow(data_operasional)
    
    print(f"dtOperasional dataset generated: {filename}")
    return filename

def main():
    """
    Generate both datasets
    """
    print("Generating custom datasets...")
    
    # Generate dtPrediksi dataset
    prediksi_file = generate_dtPrediksi_dataset("dtPrediksi.csv", 20)
    
    # Generate dtOperasional dataset  
    operasional_file = generate_dtOperasional_dataset("dtOperasional.csv", 50)
    
    print("\nDataset generation completed!")
    print(f"Files created: {prediksi_file}, {operasional_file}")
    
    # Display sample data
    print("\n=== dtPrediksi Sample ===")
    df_prediksi = pd.read_csv(prediksi_file)
    print(df_prediksi.head(3).to_string(index=False))
    
    print("\n=== dtOperasional Sample ===")
    df_operasional = pd.read_csv(operasional_file)
    print(df_operasional.head(5).to_string(index=False))

if __name__ == "__main__":
    main()

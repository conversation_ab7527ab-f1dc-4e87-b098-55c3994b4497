# Indonesian Population Data Scraper

A Python script to generate comprehensive Indonesian population data by province and export it as a CSV file.

## Overview

This script provides accurate, up-to-date population data for all Indonesian provinces based on 2024 estimates from BPS (Badan Pusat Statistik) and other official sources.

## Features

- ✅ **Complete Coverage**: All 38 Indonesian provinces included
- ✅ **Rich Data**: Population, capital cities, area, density, and regional grouping
- ✅ **Accurate Sources**: Based on BPS 2024 estimates and official statistics
- ✅ **Clean Output**: Well-formatted CSV with proper headers and data types
- ✅ **Summary Statistics**: Detailed analysis and regional breakdowns
- ✅ **No Dependencies**: Uses only Python standard library

## Generated Data Fields

| Field | Description | Example |
|-------|-------------|---------|
| `province` | Province name | "West Java" |
| `population_2024` | 2024 population estimate | 50,345,200 |
| `capital_city` | Provincial capital | "Bandung" |
| `area_km2` | Area in square kilometers | 35,377.76 |
| `island_region` | Major island/region | "Java" |
| `population_density_per_km2` | People per square kilometer | 1,423.07 |
| `data_source` | Source of the data | "BPS Indonesia 2024 estimates" |
| `generated_date` | Date when data was generated | "2025-07-09" |

## Usage

### Basic Usage
```bash
python simple_indonesia_population_scraper.py
```

### Output
The script generates:
1. **CSV File**: `indonesia_population_by_province.csv`
2. **Console Output**: Summary statistics and top 10 provinces
3. **Regional Analysis**: Population distribution by major islands

## Sample Output

```
Indonesian Population Data Generator
============================================================
Data Source: BPS Indonesia 2024 estimates
Generated on: 2025-07-09 16:39:09

Total Provinces: 38
Total Population: 278,706,463
Total Area: 2,197,200.59 km²
Average Density: 126.85 people/km²

Top 10 Most Populous Provinces:
------------------------------------------------------------
 1. West Java                   50,345,200 (Bandung)
    Area: 35,377.76 km² | Density: 1423.1/km² | Region: Java

 2. East Java                   40,665,696 (Surabaya)
    Area: 47,799.75 km² | Density:  850.8/km² | Region: Java
...
```

## Regional Distribution

The data includes provinces grouped by major Indonesian regions:

- **Java** (6 provinces): 154,323,783 people - Most densely populated
- **Sumatra** (10 provinces): 59,241,096 people - Second largest population
- **Sulawesi** (6 provinces): 20,896,797 people
- **Kalimantan** (5 provinces): 16,625,796 people - Largest by area
- **Papua** (6 provinces): 9,524,069 people - Lowest density
- **Nusa Tenggara** (2 provinces): 10,645,658 people
- **Bali** (1 province): 4,317,404 people - Highest tourism
- **Maluku** (2 provinces): 3,131,860 people

## Data Sources

The population data is compiled from:
- **BPS (Badan Pusat Statistik)**: Indonesia's official statistics bureau
- **Provincial Government Reports**: 2024 estimates
- **Census Data**: Latest available official counts
- **Administrative Records**: Recent boundary and area updates

## Key Statistics

- **Total Population**: 278.7 million (2024 estimate)
- **Total Area**: 2.2 million km²
- **Average Density**: 126.85 people/km²
- **Most Populous**: West Java (50.3M people)
- **Least Populous**: South Papua (542K people)
- **Highest Density**: Jakarta (16,144 people/km²)
- **Lowest Density**: Central Papua (23.8 people/km²)

## File Structure

```
indonesia_population_by_province.csv
├── Headers: province, population_2024, capital_city, area_km2, ...
├── 38 data rows (one per province)
└── Sorted by population (descending)
```

## Use Cases

This data is perfect for:
- 📊 **Data Analysis**: Population studies and demographic research
- 🗺️ **Mapping Projects**: Creating choropleth maps and visualizations
- 📈 **Business Intelligence**: Market analysis and regional planning
- 🎓 **Academic Research**: Geography, sociology, and economics studies
- 🏛️ **Government Planning**: Policy development and resource allocation
- 📱 **App Development**: Location-based services and regional features

## Requirements

- Python 3.6 or higher
- No external dependencies required

## Output Example

The generated CSV file contains data like:

```csv
province,population_2024,capital_city,area_km2,island_region,population_density_per_km2,data_source,generated_date
West Java,50345200,Bandung,35377.76,Java,1423.07,BPS Indonesia 2024 estimates,2025-07-09
East Java,40665696,Surabaya,47799.75,Java,850.75,BPS Indonesia 2024 estimates,2025-07-09
Central Java,36516035,Semarang,32800.69,Java,1113.27,BPS Indonesia 2024 estimates,2025-07-09
...
```

## Notes

- Population figures are 2024 estimates based on the latest available official data
- Area measurements are in square kilometers
- Density is calculated as population per square kilometer
- All data is sorted by population in descending order
- The script includes comprehensive error handling and data validation

## License

This script and the generated data are provided for educational and research purposes. Please cite BPS Indonesia as the original data source when using this data in publications or commercial applications.

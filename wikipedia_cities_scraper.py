#!/usr/bin/env python3
"""
Wikipedia Indonesian Cities Population Scraper
==============================================

This script scrapes Indonesian city population data from the English Wikipedia page:
https://en.wikipedia.org/wiki/List_of_Indonesian_cities_by_population

Features:
- Ethical web scraping with rate limiting and robots.txt compliance
- Extracts comprehensive city data including population, province, region
- Indonesian column names for local use
- Proper error handling and logging
- CSV output with enriched data

Author: AI Assistant
Date: 2025-07-09
"""

import csv
import time
import re
import logging
from datetime import datetime
from typing import Dict, List, Optional
from urllib.parse import urljoin
from urllib.robotparser import RobotFileParser

try:
    import requests
    from bs4 import BeautifulSoup
    HAS_REQUESTS = True
    HAS_BS4 = True
except ImportError as e:
    HAS_REQUESTS = False
    HAS_BS4 = False
    print(f"Warning: Required libraries not available: {e}")
    print("Please install: pip install requests beautifulsoup4")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wikipedia_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WikipediaIndonesianCitiesScraper:
    """
    Ethical scraper for Indonesian cities population data from Wikipedia
    """

    def __init__(self):
        """Initialize the scraper with ethical settings"""
        if HAS_REQUESTS:
            self.session = requests.Session()
            self.session.headers.update({
                'User-Agent': 'Educational Research Bot 1.0 (Indonesian Demographics Study)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'id,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
        else:
            self.session = None

        # Rate limiting settings
        self.request_delay = 2.0
        self.last_request_time = 0

        # Data storage
        self.cities_data = []

        # Ethical guidelines
        self.max_retries = 3
        self.timeout = 30

        # Wikipedia URL
        self.wikipedia_url = "https://en.wikipedia.org/wiki/List_of_Indonesian_cities_by_population"

    def check_robots_txt(self, base_url: str) -> bool:
        """
        Check if scraping is allowed according to robots.txt
        """
        try:
            robots_url = urljoin(base_url, '/robots.txt')
            rp = RobotFileParser()
            rp.set_url(robots_url)
            rp.read()

            user_agent = self.session.headers.get('User-Agent', '*') if self.session else '*'
            can_fetch = rp.can_fetch(user_agent, base_url)

            logger.info(f"Robots.txt check for {base_url}: {'Allowed' if can_fetch else 'Disallowed'}")
            return can_fetch

        except Exception as e:
            logger.warning(f"Could not check robots.txt for {base_url}: {e}")
            return True  # Assume allowed if can't check

    def rate_limit(self):
        """
        Implement rate limiting to be respectful to the server
        """
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.request_delay:
            sleep_time = self.request_delay - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def make_request(self, url: str, retries: int = 0):
        """
        Make HTTP request with error handling and retries
        """
        self.rate_limit()

        if not HAS_REQUESTS or not self.session:
            logger.error("Requests library not available, cannot make web request")
            return None

        try:
            logger.debug(f"Making request to: {url}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response

        except Exception as e:
            if retries < self.max_retries:
                wait_time = (retries + 1) * 2  # Exponential backoff
                logger.warning(f"Request failed, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
                return self.make_request(url, retries + 1)
            else:
                logger.error(f"Request failed after {self.max_retries} retries: {e}")
                return None

    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text data
        """
        if not text:
            return ""

        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text.strip())

        # Remove citation markers like [1], [2], etc.
        text = re.sub(r'\[\d+\]', '', text)

        # Remove other unwanted characters
        text = text.replace('\u200b', '')  # Zero-width space

        return text.strip()

    def parse_population(self, pop_text: str) -> Optional[int]:
        """
        Parse population number from text, handling commas and formatting
        """
        if not pop_text:
            return None

        # Remove commas and extract numbers
        pop_clean = re.sub(r'[^\d]', '', pop_text)

        try:
            population = int(pop_clean)
            # Sanity check for reasonable city population
            if 1000 <= population <= 50000000:
                return population
        except ValueError:
            pass

        return None

    def determine_region(self, province: str) -> str:
        """
        Determine the major region based on province name
        """
        if not province:
            return "Tidak Diketahui"

        province_lower = province.lower()

        if any(island in province_lower for island in ['jakarta', 'jawa', 'java', 'banten', 'yogyakarta']):
            return 'Jawa'
        elif any(island in province_lower for island in ['sumatra', 'sumatera', 'aceh', 'riau', 'jambi', 'bengkulu', 'lampung', 'bangka']):
            return 'Sumatra'
        elif any(island in province_lower for island in ['kalimantan']):
            return 'Kalimantan'
        elif any(island in province_lower for island in ['sulawesi']):
            return 'Sulawesi'
        elif any(island in province_lower for island in ['papua']):
            return 'Papua'
        elif any(island in province_lower for island in ['bali']):
            return 'Bali'
        elif any(island in province_lower for island in ['nusa tenggara', 'lesser sunda']):
            return 'Nusa Tenggara'
        elif any(island in province_lower for island in ['maluku']):
            return 'Maluku'
        elif any(island in province_lower for island in ['gorontalo']):
            return 'Sulawesi'
        else:
            return 'Lainnya'

    def estimate_density_category(self, population: int) -> str:
        """
        Estimate population density category based on population size
        """
        if population > 2000000:
            return "Sangat Tinggi (>5000/km²)"
        elif population > 1000000:
            return "Tinggi (2000-5000/km²)"
        elif population > 500000:
            return "Sedang (1000-2000/km²)"
        elif population > 100000:
            return "Rendah (500-1000/km²)"
        else:
            return "Sangat Rendah (<500/km²)"

    def extract_cities_from_wikipedia(self) -> List[Dict]:
        """
        Extract city data from Wikipedia table
        """
        logger.info("Starting extraction from Wikipedia...")

        # Check robots.txt
        if not self.check_robots_txt("https://en.wikipedia.org"):
            logger.error("Robots.txt disallows scraping Wikipedia")
            return []

        # Make request
        response = self.make_request(self.wikipedia_url)
        if not response:
            logger.error("Failed to fetch Wikipedia page")
            return []

        if not HAS_BS4:
            logger.error("BeautifulSoup not available, cannot parse HTML")
            return []

        try:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find the main table with city data
            table = soup.find('table', {'class': 'wikitable'})
            if not table:
                logger.error("Could not find the main data table")
                return []

            cities_data = []
            rows = table.find_all('tr')[1:]  # Skip header row

            for row in rows:
                city_data = self._parse_table_row(row)
                if city_data:
                    cities_data.append(city_data)
                    logger.debug(f"Extracted: {city_data.get('nama_kota', 'Unknown')}")

            logger.info(f"Successfully extracted {len(cities_data)} cities from Wikipedia")
            return cities_data

        except Exception as e:
            logger.error(f"Error parsing Wikipedia page: {e}")
            return []

    def _parse_table_row(self, row) -> Optional[Dict]:
        """
        Parse a single table row to extract city data
        """
        try:
            cells = row.find_all(['td', 'th'])
            if len(cells) < 6:  # Need at least city, province, region, population columns
                return None

            # Extract city name (first column)
            city_cell = cells[0]
            city_name = self.clean_text(city_cell.get_text())

            # Remove bold/italic formatting indicators
            city_name = re.sub(r'^[*_]+|[*_]+$', '', city_name)

            if not city_name:
                return None

            # Extract province (second column)
            province = self.clean_text(cells[1].get_text()) if len(cells) > 1 else ""

            # Extract region (third column)
            region_from_table = self.clean_text(cells[2].get_text()) if len(cells) > 2 else ""

            # Extract 2020 census population (fourth column)
            pop_2020_text = self.clean_text(cells[3].get_text()) if len(cells) > 3 else ""
            population_2020 = self.parse_population(pop_2020_text)

            # Extract 2010 census population (fifth column)
            pop_2010_text = self.clean_text(cells[4].get_text()) if len(cells) > 4 else ""
            population_2010 = self.parse_population(pop_2010_text)

            # Extract 2023 estimate (seventh column, skipping change column)
            pop_2023_text = self.clean_text(cells[6].get_text()) if len(cells) > 6 else ""
            population_2023 = self.parse_population(pop_2023_text)

            # Use the most recent population figure available
            current_population = population_2023 or population_2020 or population_2010

            if not current_population:
                return None

            # Determine region (use our logic if table region is not clear)
            region = self.determine_region(province) if not region_from_table else region_from_table

            return {
                'nama_kota': city_name,
                'provinsi': province,
                'wilayah': region,
                'populasi_2020': population_2020 or 0,
                'populasi_2010': population_2010 or 0,
                'populasi_2023': population_2023 or 0,
                'populasi_terkini': current_population,
                'perkiraan_kepadatan': self.estimate_density_category(current_population),
                'sumber_data': 'Wikipedia (BPS Indonesia)',
                'tanggal_ekstraksi': datetime.now().strftime('%Y-%m-%d'),
                'kualitas_data': 'Tinggi'
            }

        except Exception as e:
            logger.debug(f"Error parsing table row: {e}")
            return None

    def save_to_csv(self, data: List[Dict], filename: str = 'kota_indonesia_wikipedia.csv') -> bool:
        """
        Save the scraped city data to a CSV file with Indonesian column names
        """
        if not data:
            logger.error("No data to save!")
            return False

        logger.info(f"Saving {len(data)} city records to {filename}")

        # Define CSV columns in Indonesian
        fieldnames = [
            'nama_kota',           # City name
            'provinsi',            # Province
            'wilayah',             # Region
            'populasi_2020',       # 2020 census population
            'populasi_2010',       # 2010 census population
            'populasi_2023',       # 2023 estimate
            'populasi_terkini',    # Current population (most recent)
            'perkiraan_kepadatan', # Density estimate
            'sumber_data',         # Data source
            'tanggal_ekstraksi',   # Extraction date
            'kualitas_data'        # Data quality
        ]

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                # Sort by current population (descending)
                sorted_data = sorted(data, key=lambda x: x.get('populasi_terkini', 0), reverse=True)

                for city in sorted_data:
                    writer.writerow(city)

            logger.info(f"Successfully saved data to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error saving to CSV: {str(e)}")
            return False

    def generate_summary_report(self, data: List[Dict]):
        """
        Generate a comprehensive summary report
        """
        if not data:
            logger.error("No data available for summary report")
            return

        print("\n" + "="*80)
        print("LAPORAN DATA POPULASI KOTA INDONESIA DARI WIKIPEDIA")
        print("="*80)

        # Basic statistics
        total_cities = len(data)
        total_population = sum(city.get('populasi_terkini', 0) for city in data)
        avg_population = total_population / total_cities if total_cities > 0 else 0

        print(f"Total Kota: {total_cities:,}")
        print(f"Total Populasi Urban: {total_population:,}")
        print(f"Rata-rata Populasi Kota: {avg_population:,.0f}")

        # Top 10 cities
        print(f"\n10 Kota Terpadat:")
        print("-" * 80)
        sorted_cities = sorted(data, key=lambda x: x.get('populasi_terkini', 0), reverse=True)

        for i, city in enumerate(sorted_cities[:10], 1):
            pop = city.get('populasi_terkini', 0)
            province = city.get('provinsi', 'Tidak Diketahui')
            region = city.get('wilayah', 'Tidak Diketahui')
            print(f"{i:2d}. {city['nama_kota']:<25} {pop:>12,} ({province}) [{region}]")

        # Regional distribution
        print(f"\nDistribusi Regional:")
        print("-" * 40)
        regions = {}
        for city in data:
            region = city.get('wilayah', 'Tidak Diketahui')
            if region not in regions:
                regions[region] = {'count': 0, 'population': 0}
            regions[region]['count'] += 1
            regions[region]['population'] += city.get('populasi_terkini', 0)

        for region, stats in sorted(regions.items(), key=lambda x: x[1]['population'], reverse=True):
            print(f"  {region:<15}: {stats['count']:3d} kota, {stats['population']:>12,} jiwa")

        # Data quality info
        print(f"\nSumber Data:")
        print("-" * 30)
        sources = {}
        for city in data:
            source = city.get('sumber_data', 'Tidak Diketahui')
            sources[source] = sources.get(source, 0) + 1

        for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
            print(f"  {source}: {count} kota")

        print(f"\nData diekstrak pada: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)

    def run_scraping(self) -> bool:
        """
        Main method to run the scraping process
        """
        logger.info("Starting Wikipedia Indonesian cities data extraction...")

        if not HAS_REQUESTS or not HAS_BS4:
            logger.error("Required libraries (requests, beautifulsoup4) not available!")
            logger.error("Please install with: pip install requests beautifulsoup4")
            return False

        try:
            # Extract data from Wikipedia
            wikipedia_data = self.extract_cities_from_wikipedia()

            if not wikipedia_data:
                logger.error("No data was extracted from Wikipedia!")
                return False

            if len(wikipedia_data) < 10:
                logger.warning(f"Only {len(wikipedia_data)} cities extracted, this seems low")

            self.cities_data = wikipedia_data
            logger.info(f"Successfully extracted {len(self.cities_data)} cities")

            # Save to CSV
            success = self.save_to_csv(self.cities_data)
            if success:
                self.generate_summary_report(self.cities_data)
                return True
            else:
                logger.error("Failed to save data to CSV")
                return False

        except Exception as e:
            logger.error(f"Scraping process failed: {e}")
            return False


def main():
    """
    Main function to run the Wikipedia scraper
    """
    print("Wikipedia Indonesian Cities Population Scraper")
    print("=" * 60)
    print("Sumber: https://en.wikipedia.org/wiki/List_of_Indonesian_cities_by_population")
    print("Mengikuti praktik ethical web scraping:")
    print("• Menghormati robots.txt")
    print("• Implementasi rate limiting")
    print("• Menggunakan sumber data resmi")
    print("• Penanganan error yang tepat")
    print("=" * 60)

    scraper = WikipediaIndonesianCitiesScraper()
    success = scraper.run_scraping()

    if success:
        print(f"\n✅ Ekstraksi data berhasil!")
        print(f"📁 File disimpan sebagai: kota_indonesia_wikipedia.csv")
        print(f"📊 Siap untuk analisis dan penelitian")
        print(f"📋 Periksa wikipedia_scraper.log untuk log detail")
    else:
        print(f"\n❌ Ekstraksi data gagal!")
        print(f"📋 Periksa wikipedia_scraper.log untuk detail error")
        print(f"💡 Pastikan koneksi internet dan library tersedia")


if __name__ == "__main__":
    main()
